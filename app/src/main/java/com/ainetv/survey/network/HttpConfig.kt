package com.ainetv.survey.network

import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

/**
 * 网络请求配置类
 */
object HttpConfig {
    // 基础URL，实际使用时替换为真实地址
    private var BASE_URL = "https://api.example.com/"
    
    // 超时时间
    private const val TIMEOUT = 15L
    
    // 服务器配置
    private var serverIp = ""
    private var clientNum = ""
    private var clientArea = ""
    
    /**
     * 设置服务器配置
     */
    fun setServerConfig(serverIp: String, clientNum: String, clientArea: String) {
        this.serverIp = serverIp
        this.clientNum = clientNum
        this.clientArea = clientArea
        
        // 如果serverIp不为空，更新BASE_URL
        if (serverIp.isNotEmpty()) {
            BASE_URL = "http://$serverIp/"
        }
    }
    
    /**
     * 获取客户端编号
     */
    fun getClientNum(): String = clientNum
    
    /**
     * 获取客户端区域
     */
    fun getClientArea(): String = clientArea
    
    // 创建OkHttpClient
    private val okHttpClient: OkHttpClient by lazy {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }
        
        OkHttpClient.Builder()
            .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(loggingInterceptor)
            .build()
    }
    
    // 创建Retrofit实例
    val retrofit: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
            .build()
    }
}