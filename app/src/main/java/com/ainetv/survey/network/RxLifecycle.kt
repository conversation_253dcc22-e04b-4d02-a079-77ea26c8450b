package com.ainetv.survey.network

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.OnLifecycleEvent
import io.reactivex.Observable
import io.reactivex.ObservableTransformer
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

/**
 * RxJava生命周期管理工具类
 */
class RxLifecycle private constructor(private val lifecycleOwner: LifecycleOwner) : LifecycleObserver {
    private val compositeDisposable = CompositeDisposable()

    init {
        lifecycleOwner.lifecycle.addObserver(this)
    }

    /**
     * 添加订阅
     */
    fun addDisposable(disposable: Disposable) {
        compositeDisposable.add(disposable)
    }

    /**
     * 在生命周期结束时取消所有订阅
     */
    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        compositeDisposable.clear()
        lifecycleOwner.lifecycle.removeObserver(this)
    }

    companion object {
        /**
         * 创建RxLifecycle实例
         */
        fun with(lifecycleOwner: LifecycleOwner): RxLifecycle {
            return RxLifecycle(lifecycleOwner)
        }
    }

    /**
     * 绑定生命周期
     */
    fun <T> bindToLifecycle(): ObservableTransformer<T, T> {
        return ObservableTransformer { upstream ->
            upstream.doOnSubscribe { disposable ->
                addDisposable(disposable)
            }
        }
    }
}