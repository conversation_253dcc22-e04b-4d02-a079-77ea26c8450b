package com.ainetv.survey.network

import com.ainetv.survey.utils.LoadingManager
import io.reactivex.Observer
import io.reactivex.disposables.Disposable

/**
 * HTTP请求回调
 * @param T 响应数据类型
 */
abstract class HttpCallback<T> : Observer<T> {
    private val showLoading: Boolean
    
    constructor(showLoading: Boolean = true) {
        this.showLoading = showLoading
    }
    
    override fun onSubscribe(d: Disposable) {
        if (showLoading) {
            LoadingManager.showLoading()
        }
    }

    override fun onNext(t: T) {
        if (showLoading) {
            LoadingManager.hideLoading()
        }
        onSuccess(t)
    }

    override fun onError(e: Throwable) {
        if (showLoading) {
            LoadingManager.hideLoading()
        }
        
        if (e is ApiException) {
            onFailure(e.code, e.message)
        } else {
            onFailure(-1, e.message ?: "未知错误")
        }
    }

    override fun onComplete() {
        if (showLoading) {
            LoadingManager.hideLoading()
        }
    }

    /**
     * 请求成功回调
     */
    abstract fun onSuccess(data: T)

    /**
     * 请求失败回调
     */
    abstract fun onFailure(code: Int, msg: String)
}