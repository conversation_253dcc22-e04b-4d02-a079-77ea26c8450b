package com.ainetv.survey.network

import androidx.lifecycle.LifecycleOwner
import io.reactivex.Observable
import io.reactivex.functions.Function

/**
 * HTTP请求封装类
 * @param T 响应数据类型
 */
class HttpRequest<T> private constructor(private val observable: Observable<T>) {

    /**
     * 设置线程调度
     */
    fun scheduler(): HttpRequest<T> {
        return HttpRequest(observable.compose(RxSchedulers.ioToMain()))
    }

    /**
     * 绑定生命周期
     */
    fun lifecycle(lifecycleOwner: LifecycleOwner): HttpRequest<T> {
        return HttpRequest(observable.compose(RxLifecycle.with(lifecycleOwner).bindToLifecycle()))
    }

    /**
     * 转换响应数据
     */
    fun <R> map(function: Function<T, R>): HttpRequest<R> {
        return HttpRequest(observable.map(function))
    }

    /**
     * 执行请求
     */
    fun execute(callback: HttpCallback<T>) {
        observable.subscribe(callback)
    }

    companion object {
        /**
         * 创建请求
         */
        fun <T> create(observable: Observable<T>): HttpRequest<T> {
            return HttpRequest(observable)
        }
    }
}