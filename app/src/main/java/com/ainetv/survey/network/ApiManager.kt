package com.ainetv.survey.network

import com.ainetv.survey.model.SurveyResponse
import com.ainetv.survey.model.SurveySubmitRequest

/**
 * API管理类
 */
object ApiManager {
    private val apiService: ApiService by lazy {
        HttpConfig.retrofit.create(ApiService::class.java)
    }

    /**
     * 获取问卷调查
     * @param surveyId 问卷ID
     */
    fun getSurvey(surveyId: String): HttpRequest<SurveyResponse> {
        return HttpRequest.create(apiService.getSurvey(surveyId))
    }

    /**
     * 获取问卷表单
     */
    fun getSurveyForm(): HttpRequest<SurveyResponse> {
        return HttpRequest.create(apiService.getSurveyForm())
    }
    
    /**
     * 提交问卷答案
     * @param request 提交请求数据
     */
    fun submitSurvey(request: SurveySubmitRequest): HttpRequest<BaseResponse<Any>> {
        return HttpRequest.create(apiService.submitSurvey(request))
    }
}