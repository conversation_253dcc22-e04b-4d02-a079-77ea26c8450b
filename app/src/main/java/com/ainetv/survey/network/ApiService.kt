package com.ainetv.survey.network

import com.ainetv.survey.model.SurveyResponse
import com.ainetv.survey.model.SurveySubmitRequest
import io.reactivex.Observable
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * API服务接口
 */
interface ApiService {
    /**
     * 获取问卷调查
     * @param surveyId 问卷ID
     */
    @GET("surveys/{surveyId}")
    fun getSurvey(@Path("surveyId") surveyId: String): Observable<SurveyResponse>

    @GET("sauna/index.php/api/survey/form")
    fun getSurveyForm(): Observable<SurveyResponse>
    
    /**
     * 提交问卷答案
     * @param request 提交请求数据
     */
    @POST("sauna/index.php/api/mobile/survey/submit")
    fun submitSurvey(@Body request: SurveySubmitRequest): Observable<BaseResponse<Any>>
}