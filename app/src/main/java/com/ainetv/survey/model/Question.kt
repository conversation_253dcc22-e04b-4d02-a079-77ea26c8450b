package com.ainetv.survey.model

/**
 * Base class for all question types
 */
abstract class Question(
    val id: String,
    val dimensionTitle: String,
    val questionText: String,
    val questionType: String
) {
    abstract fun validate(): Boolean
}

/**
 * Single choice question implementation (type "1")
 */
class SingleChoiceQuestion(
    id: String,
    dimensionTitle: String,
    questionText: String,
    val options: List<Option>
) : Question(id, dimensionTitle, questionText, "1") {
    
    override fun validate(): Boolean {
        return options.isNotEmpty()
    }
}

/**
 * Open-ended question implementation (type "2")
 */
class OpenEndedQuestion(
    id: String,
    dimensionTitle: String,
    questionText: String
) : Question(id, dimensionTitle, questionText, "2") {
    
    override fun validate(): <PERSON><PERSON>an {
        return true
    }
}

/**
 * Option model for questions with choices
 */
data class Option(
    val id: String,
    val optionText: String,
    val optionSort: String
)