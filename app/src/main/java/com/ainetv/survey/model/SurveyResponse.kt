package com.ainetv.survey.model

/**
 * Response model for API requests
 */
data class SurveyResponse(
    val state: Boolean,
    val data: SurveyData?,
    val error: String,
    val messageType: String
)

/**
 * Data container for survey information
 */
data class SurveyData(
    val survey: SurveyInfo,
    val questions: List<QuestionData>
)

/**
 * Survey information model
 */
data class SurveyInfo(
    val id: String,
    val title: String,
    val description: String
)

/**
 * Question data model for API responses
 */
data class QuestionData(
    val id: String,
    val dimension_title: String,
    val question_text: String,
    val question_type: String,
    val options: List<OptionData>,
    var selectedOptionId: Int?,
    var openAnswer: String = ""
)

/**
 * Option data model for API responses
 */
data class OptionData(
    val id: String,
    val option_text: String,
    val option_sort: String
)