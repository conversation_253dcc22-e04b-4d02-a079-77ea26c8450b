package com.ainetv.survey.model

/**
 * 问卷提交数据模型
 */
data class SurveySubmission(
    val surveyId: String,
    val answers: List<Answer>,
    val clientNum: String,
    val clientArea: String
)

/**
 * 问题回答的基类
 */
sealed class Answer(
    val questionId: String,
    val questionType: String
)

/**
 * 单选题回答
 */
data class SingleChoiceAnswer(
    val qId: String,
    val selectedOptionId: String
) : Answer(qId, "1")

/**
 * 开放问答题回答
 */
data class OpenEndedAnswer(
    val qId: String,
    val answer: String
) : Answer(qId, "2")