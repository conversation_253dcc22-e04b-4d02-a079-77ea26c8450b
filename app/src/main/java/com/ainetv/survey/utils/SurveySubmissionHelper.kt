package com.ainetv.survey.utils

import com.ainetv.survey.model.*
import com.ainetv.survey.network.HttpConfig

/**
 * 问卷提交数据处理工具类
 */
object SurveySubmissionHelper {
    
    /**
     * 生成问卷提交请求数据
     * 
     * @param surveyId 问卷ID
     * @param questions 问卷问题列表
     * @param selectedOptions 单选题选项映射 (问题位置 -> 选项位置)
     * @param openAnswers 开放问答题答案映射 (问题位置 -> 答案内容)
     * @param handCard 手牌号
     * @return 问卷提交请求数据对象
     */
    fun createSubmitRequest(
        surveyId: String,
        questions: List<QuestionData>,
        selectedOptions: Map<Int, Int>,
        openAnswers: Map<Int, String>,
        handCard: String
    ): SurveySubmitRequest {
        val answers = mutableListOf<AnswerRequest>()
        
        // 处理单选题答案
        selectedOptions.forEach { (position, optionIndex) ->
            val question = questions[position]
            if (question.question_type == "1" && optionIndex >= 0 && optionIndex < question.options.size) {
                val selectedOption = question.options[optionIndex]
                answers.add(AnswerRequest(
                    questionId = question.id,
                    questionType = question.question_type,
                    answer = selectedOption.id
                ))
            }
        }
        
        // 处理开放问答题答案
        openAnswers.forEach { (position, answerText) ->
            val question = questions[position]
            if (question.question_type == "2" && answerText.isNotBlank()) {
                answers.add(AnswerRequest(
                    questionId = question.id,
                    questionType = question.question_type,
                    answer = answerText
                ))
            }
        }
        
        // 从HttpConfig获取clientNum
        val clientNum = HttpConfig.getClientNum()
        
        return SurveySubmitRequest(
            surveyId = surveyId,
            clientNum = clientNum,
            handCard = handCard,
            answers = answers
        )
    }
}