package com.ainetv.survey.utils

import android.view.View
import android.widget.ProgressBar

/**
 * 加载状态管理类
 */
object LoadingManager {
    private var loadingView: ProgressBar? = null
    private var showLoading = true

    /**
     * 设置加载视图
     */
    fun setLoadingView(view: ProgressBar) {
        loadingView = view
    }

    /**
     * 设置是否显示加载状态
     */
    fun setShowLoading(show: <PERSON>ole<PERSON>) {
        showLoading = show
    }

    /**
     * 显示加载状态
     */
    fun showLoading() {
        if (showLoading) {
            loadingView?.visibility = View.VISIBLE
        }
    }

    /**
     * 隐藏加载状态
     */
    fun hideLoading() {
        loadingView?.visibility = View.GONE
    }
}