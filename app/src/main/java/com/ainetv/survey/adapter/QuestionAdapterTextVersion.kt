package com.ainetv.survey.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.ainetv.survey.R
import com.ainetv.survey.model.OptionData
import com.ainetv.survey.model.QuestionData

/**
 * 使用TextView替代RadioGroup的测试版本
 * 用于验证是否是RadioGroup特有的状态同步问题
 */
class QuestionAdapterTextVersion : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val TAG: String = "QuestionAdapterTextVersion"
    internal val questions = mutableListOf<QuestionData>()
    
    var onOptionSelectedListener: ((Int, Int) -> Unit)? = null
    var onOpenAnswerChangedListener: ((Int, String) -> Unit)? = null
    var onSubmitClickListener: ((String) -> Unit)? = null
    
    companion object {
        private const val TYPE_SINGLE_CHOICE = 1
        private const val TYPE_OPEN_ENDED = 2
        private const val TYPE_SUBMIT_BUTTON = 3
        private const val MAX_CHARS = 100
    }
    
    fun updateQuestions(newQuestions: List<QuestionData>) {
        questions.clear()
        questions.addAll(newQuestions)
        notifyDataSetChanged()
    }
    
    override fun getItemViewType(position: Int): Int {
        return if (position == questions.size) {
            TYPE_SUBMIT_BUTTON
        } else {
            when (questions[position].question_type) {
                "1" -> TYPE_SINGLE_CHOICE
                "2" -> TYPE_OPEN_ENDED
                else -> TYPE_SINGLE_CHOICE
            }
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_SINGLE_CHOICE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_question_text_version, parent, false)
                SingleChoiceTextViewHolder(view)
            }
            TYPE_OPEN_ENDED -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_question, parent, false)
                OpenEndedViewHolder(view)
            }
            TYPE_SUBMIT_BUTTON -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_submit_button, parent, false)
                SubmitButtonViewHolder(view)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_question_text_version, parent, false)
                SingleChoiceTextViewHolder(view)
            }
        }
    }
    
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is SingleChoiceTextViewHolder -> {
                val question = questions[position]
                holder.bind(question, position)
            }
            is OpenEndedViewHolder -> {
                val question = questions[position]
                holder.bind(question, position)
            }
            is SubmitButtonViewHolder -> holder.bind()
        }
    }
    
    override fun getItemCount(): Int = questions.size + 1
    
    /**
     * 使用TextView实现的单选题ViewHolder
     */
    inner class SingleChoiceTextViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_question_title)
        private val tvText: TextView = itemView.findViewById(R.id.tv_question_text)
        private val optionsContainer: LinearLayout = itemView.findViewById(R.id.ll_options_container)
        private val optionViews = mutableListOf<TextView>()

        @SuppressLint("LongLogTag")
        fun bind(question: QuestionData, position: Int) {
            tvTitle.text = question.dimension_title
            tvText.text = question.question_text

            // 清除所有选项视图
            optionsContainer.removeAllViews()
            optionViews.clear()

            // 添加选项视图
            question.options.forEachIndexed { index, option ->
                addOptionTextView(option, index, position)
            }

            Log.d("SingleChoiceTextViewHolder", "question.selectedOptionId ${question.question_text} : ${question.selectedOptionId}")

            // 恢复选中状态
            updateOptionStates(question.selectedOptionId)
        }

        @SuppressLint("LongLogTag")
        private fun addOptionTextView(option: OptionData, index: Int, questionPosition: Int) {
            val optionView = LayoutInflater.from(itemView.context)
                .inflate(R.layout.item_option_text, optionsContainer, false) as TextView
            
            // Use emojis based on option text
            val emoji = when {
                option.option_text.contains("非常好", ignoreCase = true) -> "😍 "
                option.option_text.contains("好", ignoreCase = true) -> "😊 "
                option.option_text.contains("优秀", ignoreCase = true) -> "😊 "
                option.option_text.contains("中", ignoreCase = true) -> "😐 "
                option.option_text.contains("一般", ignoreCase = true) -> "😐 "
                option.option_text.contains("差", ignoreCase = true) -> "😔 "
                else -> ""
            }
            
            optionView.text = "$emoji${option.option_text}"
            
            // 设置点击监听器
            optionView.setOnClickListener {
                val question = questions[questionPosition]
                question.selectedOptionId = index
                updateOptionStates(index)
                onOptionSelectedListener?.invoke(questionPosition, index)
                Log.d("SingleChoiceTextViewHolder", "Selected option $index for question at position $questionPosition")
            }
            
            optionViews.add(optionView)
            optionsContainer.addView(optionView)
        }
        
        private fun updateOptionStates(selectedIndex: Int?) {
            optionViews.forEachIndexed { index, textView ->
                if (index == selectedIndex) {
                    // 选中状态
                    textView.setBackgroundResource(R.drawable.bg_option_selected)
                    textView.setTextColor(ContextCompat.getColor(itemView.context, android.R.color.white))
                } else {
                    // 未选中状态
                    textView.setBackgroundResource(R.drawable.bg_option_normal)
                    textView.setTextColor(ContextCompat.getColor(itemView.context, R.color.option_text_normal))
                }
            }
        }
    }

    fun getOpenAnswers(): Map<Int, String> {
        val result = mutableMapOf<Int, String>()
        questions.forEachIndexed { index, question ->
            if (question.openAnswer.isNotEmpty()) {
                result[index] = question.openAnswer
            }
        }
        return result
    }
    
    // 其他ViewHolder类保持不变...
    inner class OpenEndedViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        // 实现省略，与原版本相同
        fun bind(question: QuestionData, position: Int) {
            // 实现省略
        }
    }
    
    inner class SubmitButtonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        // 实现省略，与原版本相同
        fun bind() {
            // 实现省略
        }
    }
    
    // 辅助方法
    fun getSelectedOptions(): Map<Int, Int> {
        val selectedOptions = mutableMapOf<Int, Int>()
        questions.forEachIndexed { index, question ->
            if (question.question_type == "1" && question.selectedOptionId != null) {
                selectedOptions[index] = question.selectedOptionId!!
            }
        }
        return selectedOptions
    }
    
    private fun areAllSingleChoiceQuestionsAnswered(): Boolean {
        val singleChoiceQuestions = questions.filter { question -> 
            question.question_type == "1" 
        }
        
        return singleChoiceQuestions.isNotEmpty() && 
               singleChoiceQuestions.all { question ->
                   question.selectedOptionId != null
               }
    }
}
