package com.ainetv.survey.adapter

import android.app.AlertDialog
import android.content.Context
import android.text.Editable
import android.text.InputFilter
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.RecyclerView
import com.ainetv.survey.R
import com.ainetv.survey.model.OptionData
import com.ainetv.survey.model.QuestionData

class QuestionAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private val TAG: String = "QuestionAdapter"
    internal val questions = mutableListOf<QuestionData>()
    // 不再需要单独的Map来存储选项和回答，直接使用QuestionData对象中的字段

    var onOptionSelectedListener: ((Int, Int) -> Unit)? = null
    var onOpenAnswerChangedListener: ((Int, String) -> Unit)? = null
    var onSubmitClickListener: ((String) -> Unit)? = null

    companion object {
        private const val TYPE_SINGLE_CHOICE = 1
        private const val TYPE_OPEN_ENDED = 2
        private const val TYPE_SUBMIT_BUTTON = 3
        private const val MAX_CHARS = 100
    }

    fun updateQuestions(newQuestions: List<QuestionData>) {
        questions.clear()
        questions.addAll(newQuestions)
        notifyDataSetChanged()
    }

    override fun getItemViewType(position: Int): Int {
        return if (position == questions.size) {
            TYPE_SUBMIT_BUTTON
        } else {
            when (questions[position].question_type) {
                "1" -> TYPE_SINGLE_CHOICE
                "2" -> TYPE_OPEN_ENDED
                else -> TYPE_SINGLE_CHOICE // Default to single choice
            }
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_SINGLE_CHOICE -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_question, parent, false)
                SingleChoiceViewHolder(view)
            }
            TYPE_OPEN_ENDED -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_question, parent, false)
                OpenEndedViewHolder(view)
            }
            TYPE_SUBMIT_BUTTON -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_submit_button, parent, false)
                SubmitButtonViewHolder(view)
            }
            else -> {
                val view = LayoutInflater.from(parent.context)
                    .inflate(R.layout.item_question, parent, false)
                SingleChoiceViewHolder(view)
            }
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is SingleChoiceViewHolder -> {
                val question = questions[position]
                holder.bind(question, position)
            }
            is OpenEndedViewHolder -> {
                val question = questions[position]
                holder.bind(question, position)
            }
            is SubmitButtonViewHolder -> holder.bind()
        }
    }

    // 由于状态直接存储在QuestionData对象中，不再需要在ViewHolder回收时特别处理
    override fun onViewRecycled(holder: RecyclerView.ViewHolder) {
        super.onViewRecycled(holder)
    }

    override fun getItemCount(): Int = questions.size + 1 // +1 for submit button

    inner class SingleChoiceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_question_title)
        private val tvText: TextView = itemView.findViewById(R.id.tv_question_text)
        private val optionsGroup: RadioGroup = itemView.findViewById(R.id.rg_options_container)

        fun bind(question: QuestionData, position: Int) {
            tvTitle.text = question.dimension_title
            tvText.text = question.question_text

            // 清除所有视图和监听器
            optionsGroup.setOnCheckedChangeListener(null)
            optionsGroup.removeAllViews()
            optionsGroup.clearCheck()

            // 添加选项视图
            question.options.forEachIndexed { index, option ->
                addOptionView(option, index, position)
            }

            Log.d(TAG, "question.selectedOptionId ${question.question_text} : ${question.selectedOptionId}")

            // 延迟恢复选中状态，确保所有视图都已添加完成
            optionsGroup.post {
                if (question.selectedOptionId != null) {
                    // 使用生成的唯一ID来恢复状态
                    val uniqueId = generateUniqueId(position, question.selectedOptionId!!)
                    optionsGroup.check(uniqueId)
                } else {
                    optionsGroup.clearCheck()
                }

                // 设置监听器
                optionsGroup.setOnCheckedChangeListener { group, checkedId ->
                    if (checkedId != -1) {
                        // 从唯一ID中提取原始选项索引
                        val optionIndex = extractOptionIndex(checkedId, position)
                        question.selectedOptionId = optionIndex
                        Log.d(TAG, "Selected option $optionIndex for question at position $position")
                        onOptionSelectedListener?.invoke(position, optionIndex)
                        updateSubmitButtonState()
                    }
                }
            }
        }

        private fun addOptionView(option: OptionData, index: Int, questionPosition: Int) {
            val optionView = LayoutInflater.from(itemView.context)
                .inflate(R.layout.item_option, optionsGroup, false) as RadioButton

            // 生成唯一ID，避免不同问题间的ID冲突
            val uniqueId = generateUniqueId(questionPosition, index)
            optionView.id = uniqueId

            // Use emojis based on option text
            val emoji = when {
                option.option_text.contains("非常好", ignoreCase = true) -> "😍 "
                option.option_text.contains("好", ignoreCase = true) -> "😊 "
                option.option_text.contains("优秀", ignoreCase = true) -> "😊 "
                option.option_text.contains("中", ignoreCase = true) -> "😐 "
                option.option_text.contains("一般", ignoreCase = true) -> "😐 "
                option.option_text.contains("差", ignoreCase = true) -> "😔 "
                else -> ""
            }

            optionView.text = "$emoji${option.option_text}"

            optionsGroup.addView(optionView)
        }

        // 生成唯一ID的方法
        private fun generateUniqueId(questionPosition: Int, optionIndex: Int): Int {
            // 使用问题位置和选项索引生成唯一ID
            // 假设最多有1000个问题，每个问题最多有100个选项
            return questionPosition * 1000 + optionIndex + 10000 // 加10000避免与系统ID冲突
        }

        // 从唯一ID中提取选项索引
        private fun extractOptionIndex(uniqueId: Int, questionPosition: Int): Int {
            return (uniqueId - 10000) % 1000
        }
    }

    inner class OpenEndedViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvTitle: TextView = itemView.findViewById(R.id.tv_question_title)
        private val tvText: TextView = itemView.findViewById(R.id.tv_question_text)
        private val optionsGroup: RadioGroup = itemView.findViewById(R.id.rg_options_container)

        fun bind(question: QuestionData, position: Int) {
            tvTitle.text = question.dimension_title
            tvText.text = question.question_text

            optionsGroup.removeAllViews()

            // Add open-ended input field
            val openAnswerView = LayoutInflater.from(itemView.context)
                .inflate(R.layout.item_open_question, optionsGroup, false)

            val editText = openAnswerView.findViewById<EditText>(R.id.et_open_answer)
            val charCountView = openAnswerView.findViewById<TextView>(R.id.tv_char_count)
            val doneButton = openAnswerView.findViewById<Button>(R.id.btn_done)

            // 初始状态下隐藏完成按钮
            doneButton.visibility = View.GONE

            // Set existing answer if available
            editText.setText(question.openAnswer?:"")
            charCountView.text = "${question?.openAnswer?.length?:0}/$MAX_CHARS"

            // 设置焦点变化监听器
            editText.setOnFocusChangeListener { _, hasFocus ->
                doneButton.visibility = if (hasFocus) View.VISIBLE else View.GONE
            }

            // Listen for text changes
            editText.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

                override fun afterTextChanged(s: Editable?) {
                    val answer = s.toString()
                    question.openAnswer = answer
                    charCountView.text = "${answer.length}/$MAX_CHARS"
                    onOpenAnswerChangedListener?.invoke(position, answer)
                }
            })

            // Set up done button to hide keyboard
            doneButton.setOnClickListener {
                hideKeyboard(editText)
            }

            optionsGroup.addView(openAnswerView)
        }

        private fun hideKeyboard(view: View) {
            val imm = view.context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
            view.clearFocus()
        }
    }

    fun getSelectedOptions(): Map<Int, Int> {
        val selectedOptions = mutableMapOf<Int, Int>()
        questions.forEachIndexed { index, question ->
            if (question.question_type == "1" && question.selectedOptionId != null) {
                selectedOptions[index] = question.selectedOptionId!!
            }
        }
        return selectedOptions
    }

    fun getOpenAnswers(): Map<Int, String> {
        val result = mutableMapOf<Int, String>()
        questions.forEachIndexed { index, question ->
            if (question.openAnswer.isNotEmpty()) {
                result[index] = question.openAnswer
            }
        }
        return result
    }

    private fun areAllSingleChoiceQuestionsAnswered(): Boolean {
        val singleChoiceQuestions = questions.filter { question ->
            question.question_type == "1"
        }

        return singleChoiceQuestions.isNotEmpty() &&
               singleChoiceQuestions.all { question ->
                   question.selectedOptionId != null
               }
    }

    fun updateSubmitButtonState() {
        notifyItemChanged(questions.size)
    }

    inner class SubmitButtonViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val submitButton: TextView = itemView.findViewById(R.id.tv_commit)

        fun bind() {
            updateSubmitButtonState()
            submitButton.setOnClickListener {
                val allSingleChoiceQuestionsAnswered = areAllSingleChoiceQuestionsAnswered()
                if (allSingleChoiceQuestionsAnswered) {
                    showHandCardInputDialog(itemView.context)
                } else {
                    android.widget.Toast.makeText(
                        itemView.context,
                        "请回答所有单选题",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }

        private fun showHandCardInputDialog(context: Context) {
            val dialog = AlertDialog.Builder(context).create()
            val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_hand_card_input, null)

            val etHandCard = dialogView.findViewById<EditText>(R.id.et_hand_card)
            val btnCancel = dialogView.findViewById<Button>(R.id.btn_cancel)
            val btnConfirm = dialogView.findViewById<Button>(R.id.btn_confirm)

            // 只允许输入字母和数字
            etHandCard.filters = arrayOf(InputFilter { source, start, end, dest, dstart, dend ->
                val regex = Regex("^[a-zA-Z0-9]*$")
                if (source.toString().matches(regex)) {
                    null
                } else {
                    ""
                }
            })

            btnCancel.setOnClickListener {
                dialog.dismiss()
            }

            btnConfirm.setOnClickListener {
                val handCardNumber = etHandCard.text.toString()
                if (handCardNumber.isNotEmpty()) {
                    dialog.dismiss()
                    onSubmitClickListener?.invoke(handCardNumber)
                } else {
                    Toast.makeText(context, "请输入手牌号", Toast.LENGTH_SHORT).show()
                }
            }

            dialog.setView(dialogView)
            dialog.setCancelable(false)
            dialog.show()

            // 设置对话框居中显示
            val window = dialog.window
            window?.let {
                val layoutParams = WindowManager.LayoutParams()
                layoutParams.copyFrom(it.attributes)
                layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT
                layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
                layoutParams.gravity = Gravity.CENTER
                it.attributes = layoutParams
            }
        }

        fun updateSubmitButtonState() {
            val allSingleChoiceQuestionsAnswered = areAllSingleChoiceQuestionsAnswered()
            if (allSingleChoiceQuestionsAnswered) {
                submitButton.setBackgroundResource(R.drawable.bg_option_selected)
                submitButton.setTextColor(0xFFFFFFFF.toInt())
            } else {
                submitButton.setBackgroundResource(R.drawable.bg_commit_default)
                submitButton.setTextColor(0xFF666666.toInt())
            }
        }
    }
}