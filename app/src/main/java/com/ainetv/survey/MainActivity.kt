package com.ainetv.survey

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.ainetv.survey.adapter.QuestionAdapter
import com.ainetv.survey.databinding.ActivityMainBinding
import com.ainetv.survey.model.SurveyResponse
import com.ainetv.survey.model.SurveySubmission
import com.ainetv.survey.network.HttpConfig
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import com.ainetv.survey.network.ApiManager
import com.ainetv.survey.network.BaseResponse
import com.ainetv.survey.network.HttpCallback
import com.ainetv.survey.utils.LoadingManager
import com.ainetv.survey.utils.SurveySubmissionHelper

class MainActivity : AppCompatActivity() {
    private var _binding: ActivityMainBinding? = null
    private val binding get() = _binding!!
    private val questionAdapter = QuestionAdapter()
    
    companion object {
        private const val TAG = "MainActivity"
        private const val TARGET_PACKAGE = "com.tongshi.tongshi"
        private const val PREF_NAME = "com.tongshi.tongshi_preferences"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        _binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // 初始化加载状态管理器
        LoadingManager.setLoadingView(binding.loadingIndicator)
        
        // 获取目标应用的SharedPreferences配置
        getTargetAppConfig()
        
        initVersionInfo()
        setupRecyclerView()
        
        // 加载问卷表单
        loadSurveyForm()
    }
    
    private fun setupRecyclerView() {
        binding.rvQuestions.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = questionAdapter.apply {
                onSubmitClickListener = { handCard ->
                    submitSurvey(handCard)
                }
            }
        }
    }
    
    private fun submitSurvey(handCard: String) {
        val request = SurveySubmissionHelper.createSubmitRequest(
            surveyId = "1",
            questions = questionAdapter.questions,
            selectedOptions = questionAdapter.getSelectedOptions(),
            openAnswers = questionAdapter.getOpenAnswers(),
            handCard = handCard
        )
        
        ApiManager.submitSurvey(request)
            .scheduler()
            .lifecycle(this)
            .execute(object : HttpCallback<BaseResponse<Any>>(true) {
                override fun onSuccess(data: BaseResponse<Any>) {
                    Toast.makeText(this@MainActivity, "感谢您的参与!", Toast.LENGTH_SHORT).show()
                    finish()
                }

                override fun onFailure(code: Int, msg: String) {
                    Toast.makeText(this@MainActivity, msg, Toast.LENGTH_SHORT).show()
                }
            })
    }
    
    private fun getTargetAppConfig() {
        try {
            // 获取目标应用的Context
            val targetContext = createPackageContext(TARGET_PACKAGE, Context.CONTEXT_IGNORE_SECURITY)
            
            // 获取目标应用的SharedPreferences
            val sharedPreferences = targetContext.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
            
            // 读取目标配置
            val serverIp = sharedPreferences.getString("serverip", "") ?: ""
            val clientNum = sharedPreferences.getString("client_num", "") ?: ""
            val clientArea = sharedPreferences.getString("client_area", "") ?: ""
            
            // 设置到HttpConfig中
            HttpConfig.setServerConfig(serverIp, clientNum, clientArea)
            
            Log.d(TAG, "获取目标应用配置成功: serverIp=$serverIp, clientNum=$clientNum, clientArea=$clientArea")
        } catch (e: Exception) {
            Log.e(TAG, "获取目标应用配置失败", e)
        }
    }
    
    private fun initVersionInfo() {
        try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            binding.tvVersion.text = "Version: ${packageInfo.versionName}"
        } catch (e: Exception) {
            binding.tvVersion.text = "Version: Unknown"
        }
    }
    
    /**
     * 加载问卷表单数据
     */
    private fun loadSurveyForm() {
        // 调用API获取问卷表单
        ApiManager.getSurveyForm()
            .scheduler()
            .lifecycle(this)
            .execute(object : HttpCallback<SurveyResponse>(true) {
                override fun onSuccess(data: SurveyResponse) {
                    Log.d(TAG, "rece : ${data?.toString()}")
                    if (data?.state == true && data?.data?.questions?.isNotEmpty() == true) {
                        questionAdapter.updateQuestions(data.data.questions)
                    } else {
                        Toast.makeText(this@MainActivity, "暂无问卷数据", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(code: Int, msg: String) {
                    Log.d(TAG, "msg : ${msg}")
                    Toast.makeText(this@MainActivity, "暂无问卷数据 Exception!", Toast.LENGTH_SHORT).show()
                }
            })
    }
    
    override fun onDestroy() {
        super.onDestroy()
        _binding = null
    }
}