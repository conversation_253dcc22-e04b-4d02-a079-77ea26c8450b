<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="请您输入手牌号"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="16dp" />

    <EditText
        android:id="@+id/et_hand_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="请输入手牌号"
        android:gravity="center"
        android:inputType="text"
        android:maxLines="1"
        android:layout_marginBottom="16dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/selector_cancel_button"
            android:text="取消"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="确认"
            android:background="@drawable/selector_button_background"
            android:textColor="#FFFFFF" />
    </LinearLayout>
</LinearLayout>