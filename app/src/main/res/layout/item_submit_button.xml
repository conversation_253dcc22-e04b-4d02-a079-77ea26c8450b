<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:padding="16dp">

    <TextView
        android:id="@+id/tv_commit"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="提交"
        android:gravity="center"
        android:textSize="16sp"
        android:paddingBottom="8dp"
        android:paddingTop="8dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

</androidx.constraintlayout.widget.ConstraintLayout>