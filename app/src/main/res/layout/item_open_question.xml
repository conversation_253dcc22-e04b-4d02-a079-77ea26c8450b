<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <EditText
        android:id="@+id/et_open_answer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="120dp"
        android:padding="12dp"
        android:gravity="top|start"
        android:background="@drawable/bg_option_normal"
        android:textSize="16sp"
        android:textColor="#333333"
        android:hint="请输入您的回答..."
        android:maxLength="100"
        android:inputType="textMultiLine" />
        
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="4dp">
        
        <TextView
            android:id="@+id/tv_char_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="0/100"
            android:textSize="12sp"
            android:textColor="#666666" />
            
        <Button
            android:id="@+id/btn_done"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginEnd="8dp"
            android:text="完成"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:background="@drawable/bg_option_selected"
            android:minWidth="60dp"
            android:padding="4dp" />
    </LinearLayout>
</LinearLayout>